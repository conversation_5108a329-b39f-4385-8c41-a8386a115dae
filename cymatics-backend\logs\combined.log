{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase logging enabled for development\u001b[39m","timestamp":"2025-06-08 21:04:54:454"}
{"clientVersion":"5.22.0","errorCode":"P1001","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase connection failed: Can't reach database server at `localhost:5433`\u001b[39m\n\n\u001b[31mPlease make sure your database server is running at `localhost:5433`.\u001b[39m","name":"PrismaClientInitializationError","stack":"PrismaClientInitializationError: Can't reach database server at `localhost:5433`\n\nPlease make sure your database server is running at `localhost:5433`.\n    at t (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:112:2488)\n    at async connectDatabase (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\config\\database.ts:28:5)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\server.ts:45:5)","timestamp":"2025-06-08 21:05:05:55"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase logging enabled for development\u001b[39m","timestamp":"2025-06-08 21:07:36:736"}
{"clientVersion":"5.22.0","errorCode":"P1000","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase connection failed: Authentication failed against database server at `localhost`, the provided database credentials for `postgres` are not valid.\u001b[39m\n\n\u001b[31mPlease make sure to provide valid database credentials for the database server at `localhost`.\u001b[39m","name":"PrismaClientInitializationError","stack":"PrismaClientInitializationError: Authentication failed against database server at `localhost`, the provided database credentials for `postgres` are not valid.\n\nPlease make sure to provide valid database credentials for the database server at `localhost`.\n    at t (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:112:2488)\n    at async connectDatabase (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\config\\database.ts:28:5)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\server.ts:45:5)","timestamp":"2025-06-08 21:07:47:747"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase logging enabled for development\u001b[39m","timestamp":"2025-06-08 21:08:07:87"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-08 21:08:12:812"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Cymatics Backend Server is running on port 3000\u001b[39m","timestamp":"2025-06-08 21:08:12:812"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-08 21:08:12:812"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:3000/health\u001b[39m","timestamp":"2025-06-08 21:08:12:812"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API docs: http://localhost:3000/api\u001b[39m","timestamp":"2025-06-08 21:08:12:812"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mEmail service connection failed: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\u001b[39m\n\u001b[31m535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-31349ffc151sm4149856a91.48 - gsmtp\u001b[39m","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-31349ffc151sm4149856a91.48 - gsmtp","responseCode":535,"stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-31349ffc151sm4149856a91.48 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at TLSSocket.SMTPConnection._onSocketData (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:524:28)\n    at TLSSocket.emit (node:domain:489:12)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)","timestamp":"2025-06-08 21:08:20:820"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGINT. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-08 21:09:27:927"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase disconnected successfully\u001b[39m","timestamp":"2025-06-08 21:09:27:927"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase logging enabled for development\u001b[39m","timestamp":"2025-06-08 21:09:49:949"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-08 21:09:54:954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Cymatics Backend Server is running on port 3000\u001b[39m","timestamp":"2025-06-08 21:09:54:954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-08 21:09:54:954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:3000/health\u001b[39m","timestamp":"2025-06-08 21:09:54:954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API docs: http://localhost:3000/api\u001b[39m","timestamp":"2025-06-08 21:09:54:954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEmail service connected successfully\u001b[39m","timestamp":"2025-06-08 21:10:02:102"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [08/Jun/2025:15:43:25 +0000] \"GET /health HTTP/1.1\" 200 103 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-08 21:13:25:1325"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [08/Jun/2025:15:43:25 +0000] \"GET /favicon.ico HTTP/1.1\" 404 126 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-08 21:13:25:1325"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [08/Jun/2025:15:47:15 +0000] \"GET /health HTTP/1.1\" 200 103 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-08 21:17:15:1715"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:192.168.227.104 - - [08/Jun/2025:15:47:21 +0000] \"GET /health HTTP/1.1\" 200 103 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-08 21:17:21:1721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:192.168.227.104 - - [08/Jun/2025:15:47:24 +0000] \"GET /health HTTP/1.1\" 200 103 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-06-08 21:17:24:1724"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:23:41:2341"}
{"ip":"::ffff:***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:23:41:2341","url":"/api/auth/send-otp","userAgent":"okhttp/4.12.0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:*************** - - [08/Jun/2025:15:53:41 +0000] \"POST /api/auth/send-otp HTTP/1.1\" 500 497 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:23:41:2341"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase logging enabled for development\u001b[39m","timestamp":"2025-06-08 21:26:41:2641"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-08 21:26:48:2648"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Cymatics Backend Server is running on port 3000\u001b[39m","timestamp":"2025-06-08 21:26:48:2648"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-08 21:26:48:2648"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:3000/health\u001b[39m","timestamp":"2025-06-08 21:26:48:2648"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API docs: http://localhost:3000/api\u001b[39m","timestamp":"2025-06-08 21:26:48:2648"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Network access: http://0.0.0.0:3000\u001b[39m","timestamp":"2025-06-08 21:26:48:2648"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEmail service connected successfully\u001b[39m","timestamp":"2025-06-08 21:26:50:2650"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:27:05:275"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:27:05:275","url":"/api/auth/send-otp","userAgent":"okhttp/4.12.0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:15:57:05 +0000] \"POST /api/auth/send-otp HTTP/1.1\" 500 497 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:27:05:275"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:27:14:2714"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:27:14:2714","url":"/api/auth/send-otp","userAgent":"okhttp/4.12.0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:15:57:14 +0000] \"POST /api/auth/send-otp HTTP/1.1\" 500 497 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:27:14:2714"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:27:57:2757"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:27:57:2757","url":"/api/auth/send-otp","userAgent":"okhttp/4.12.0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:15:57:57 +0000] \"POST /api/auth/send-otp HTTP/1.1\" 500 497 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:27:57:2757"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase logging enabled for development\u001b[39m","timestamp":"2025-06-08 21:29:58:2958"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-08 21:30:07:307"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Cymatics Backend Server is running on port 3000\u001b[39m","timestamp":"2025-06-08 21:30:07:307"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-08 21:30:07:307"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:3000/health\u001b[39m","timestamp":"2025-06-08 21:30:07:307"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API docs: http://localhost:3000/api\u001b[39m","timestamp":"2025-06-08 21:30:07:307"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Network access: http://0.0.0.0:3000\u001b[39m","timestamp":"2025-06-08 21:30:07:307"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEmail service connected successfully\u001b[39m","timestamp":"2025-06-08 21:30:09:309"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew user created: <EMAIL>\u001b[39m","timestamp":"2025-06-08 21:30:12:3012"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mOTP email sent <NAME_EMAIL>\u001b[39m","messageId":"<<EMAIL>>","timestamp":"2025-06-08 21:30:17:3017"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mOTP <NAME_EMAIL>\u001b[39m","timestamp":"2025-06-08 21:30:17:3017"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:00:17 +0000] \"POST /api/auth/send-otp HTTP/1.1\" 200 146 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:30:17:3017"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase logging enabled for development\u001b[39m","timestamp":"2025-06-08 21:30:12:3012"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-08 21:30:19:3019"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mOTP email sent <NAME_EMAIL>\u001b[39m","messageId":"<<EMAIL>>","timestamp":"2025-06-08 21:30:39:3039"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mOTP <NAME_EMAIL>\u001b[39m","timestamp":"2025-06-08 21:30:39:3039"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:00:39 +0000] \"POST /api/auth/send-otp HTTP/1.1\" 200 146 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:30:39:3039"}
{"code":"AUTHENTICATION_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError verifying OTP: Invalid or expired OTP\u001b[39m","name":"AuthenticationError","stack":"AuthenticationError: Invalid or expired OTP\n    at AuthService.verifyOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:131:15)\n    at async verifyOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:29:22)","statusCode":401,"timestamp":"2025-06-08 21:31:23:3123"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Invalid or expired OTP\u001b[39m","method":"POST","stack":"AuthenticationError: Invalid or expired OTP\n    at AuthService.verifyOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:131:15)\n    at async verifyOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:29:22)","timestamp":"2025-06-08 21:31:23:3123","url":"/api/auth/verify-otp","userAgent":"okhttp/4.12.0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:23 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 131 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:23:3123"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully: <EMAIL>\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:44 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 473 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:44 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:44 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:44 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 278 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:44 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 725 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:44 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 709 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:44 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 805 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:44 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 340 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:44 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:44 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 181 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:44 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 270 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:01:44 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 940 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:31:44:3144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:16 +0000] \"GET /api/projects?page=1&limit=10&sortBy=updatedAt&sortOrder=desc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:16:3216"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:16 +0000] \"GET /api/projects?page=1&limit=10&sortBy=updatedAt&sortOrder=desc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:16:3216"}
{"code":"CONFLICT_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError deleting project: Cannot delete project with existing financial records\u001b[39m","name":"ConflictError","stack":"ConflictError: Cannot delete project with existing financial records\n    at ProjectService.deleteProject (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\project.service.ts:645:15)\n    at async deleteProject (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\project.controller.ts:238:22)","statusCode":409,"timestamp":"2025-06-08 21:32:22:3222"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Cannot delete project with existing financial records\u001b[39m","method":"DELETE","stack":"ConflictError: Cannot delete project with existing financial records\n    at ProjectService.deleteProject (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\project.service.ts:645:15)\n    at async deleteProject (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\project.controller.ts:238:22)","timestamp":"2025-06-08 21:32:22:3222","url":"/api/projects/2","userAgent":"okhttp/4.12.0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:22 +0000] \"DELETE /api/projects/2 HTTP/1.1\" 409 156 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:22:3222"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:27 +0000] \"GET /api/financial/income?limit=50 HTTP/1.1\" 200 884 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:27:3227"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:27 +0000] \"GET /api/financial/income?limit=50 HTTP/1.1\" 200 884 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:27:3227"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:27 +0000] \"GET /api/financial/income/chart-data?period=6months HTTP/1.1\" 200 394 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:27:3227"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:27 +0000] \"GET /api/financial/income/chart-data?period=6months HTTP/1.1\" 200 394 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:27:3227"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mUpdated finances for project CYM-2: profit=38000, received=0, pending=75000\u001b[39m","timestamp":"2025-06-08 21:32:29:3229"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mIncome deleted: Advance payment for wedding - ₹37500\u001b[39m","timestamp":"2025-06-08 21:32:29:3229"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:29 +0000] \"DELETE /api/financial/income/2 HTTP/1.1\" 200 156 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:29:3229"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mUpdated finances for project CYM-1: profit=45000, received=0, pending=50000\u001b[39m","timestamp":"2025-06-08 21:32:33:3233"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mIncome deleted: Payment for corporate event photography - ₹50000\u001b[39m","timestamp":"2025-06-08 21:32:33:3233"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:33 +0000] \"DELETE /api/financial/income/1 HTTP/1.1\" 200 156 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:33:3233"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:35 +0000] \"GET /api/financial/expenses?limit=50 HTTP/1.1\" 200 844 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:35:3235"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:35 +0000] \"GET /api/financial/expenses?limit=50 HTTP/1.1\" 200 844 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:35:3235"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mUpdated finances for project CYM-1: profit=50000, received=0, pending=50000\u001b[39m","timestamp":"2025-06-08 21:32:37:3237"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExpense deleted: Camera lens rental - ₹5000\u001b[39m","timestamp":"2025-06-08 21:32:37:3237"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:37 +0000] \"DELETE /api/financial/expenses/1 HTTP/1.1\" 200 158 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:37:3237"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mUpdated finances for project CYM-2: profit=50000, received=0, pending=75000\u001b[39m","timestamp":"2025-06-08 21:32:43:3243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExpense deleted: Flight tickets to Goa - ₹12000\u001b[39m","timestamp":"2025-06-08 21:32:43:3243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:43 +0000] \"DELETE /api/financial/expenses/2 HTTP/1.1\" 200 158 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:43:3243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:53 +0000] \"GET /api/financial/income?limit=50 HTTP/1.1\" 200 175 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:53:3253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:53 +0000] \"GET /api/financial/income/chart-data?period=6months HTTP/1.1\" 200 394 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:53:3253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:55 +0000] \"GET /api/projects?page=1&limit=10&sortBy=updatedAt&sortOrder=desc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:55:3255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProject deleted: CYM-2 - Wedding Videography\u001b[39m","timestamp":"2025-06-08 21:32:57:3257"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:57 +0000] \"DELETE /api/projects/2 HTTP/1.1\" 200 146 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:57:3257"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProject deleted: CYM-1 - Corporate Event Photography\u001b[39m","timestamp":"2025-06-08 21:32:59:3259"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:02:59 +0000] \"DELETE /api/projects/1 HTTP/1.1\" 200 146 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:32:59:3259"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:01 +0000] \"GET /api/financial/expenses?limit=50 HTTP/1.1\" 200 176 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:01:331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:02 +0000] \"GET /api/financial/income?limit=50 HTTP/1.1\" 200 175 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:02:332"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:02 +0000] \"GET /api/financial/income/chart-data?period=6months HTTP/1.1\" 200 389 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:02:332"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:03 +0000] \"GET /api/financial/expenses?limit=50 HTTP/1.1\" 200 176 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:03:333"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:05 +0000] \"GET /api/financial/expenses?limit=50 HTTP/1.1\" 200 176 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:05:335"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:06 +0000] \"GET /api/financial/income?limit=50 HTTP/1.1\" 200 175 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:06:336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:06 +0000] \"GET /api/financial/income/chart-data?period=6months HTTP/1.1\" 200 389 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:06:336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:06 +0000] \"GET /api/financial/income?limit=50 HTTP/1.1\" 200 175 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:06:336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:06 +0000] \"GET /api/financial/income/chart-data?period=6months HTTP/1.1\" 200 389 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:06:336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:09 +0000] \"GET /api/calendar/events/range?startDate=2025-05-31T18%3A30%3A00.000Z&endDate=2025-06-29T18%3A30%3A00.000Z HTTP/1.1\" 200 122 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:09:339"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:09 +0000] \"GET /api/projects?limit=1000&page=1 HTTP/1.1\" 200 170 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:09:339"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:11 +0000] \"GET /api/calendar/events/range?startDate=2025-05-31T18%3A30%3A00.000Z&endDate=2025-06-29T18%3A30%3A00.000Z HTTP/1.1\" 200 122 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:11:3311"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:11 +0000] \"GET /api/projects?limit=1000&page=1 HTTP/1.1\" 200 170 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:11:3311"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:18 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:18:3318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:18 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 169 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:18:3318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:18 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 266 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:18:3318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:18 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 805 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:18:3318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:18 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 340 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:18:3318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:18 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:18:3318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:18 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 709 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:18:3318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:18 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 151 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:18:3318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:18 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 270 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:18:3318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:18 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 667 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:18:3318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:18 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 940 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:18:3318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:25 +0000] \"GET /api/projects?limit=5000&page=1 HTTP/1.1\" 200 170 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:25:3325"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:27 +0000] \"GET /api/projects?limit=5000&page=1 HTTP/1.1\" 200 170 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:31 +0000] \"GET /api/projects?limit=5000&page=1 HTTP/1.1\" 200 170 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:31:3331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:46 +0000] \"GET /budget/investment-details HTTP/1.1\" 404 140 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:46:3346"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:46 +0000] \"GET /budget/categories HTTP/1.1\" 404 132 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:46:3346"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:46 +0000] \"GET /budget/overview HTTP/1.1\" 404 130 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:46:3346"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:46 +0000] \"GET /budget/overview HTTP/1.1\" 404 130 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:46:3346"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:46 +0000] \"GET /budget/categories HTTP/1.1\" 404 132 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:46:3346"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:46 +0000] \"GET /financial/budget HTTP/1.1\" 404 131 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:46:3346"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:46 +0000] \"GET /financial/budget HTTP/1.1\" 404 131 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:46:3346"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:51 +0000] \"GET /budget/overview HTTP/1.1\" 404 130 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:51:3351"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:51 +0000] \"GET /budget/categories HTTP/1.1\" 404 132 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:51:3351"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:51 +0000] \"GET /budget/investment-details HTTP/1.1\" 404 140 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:51:3351"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:51 +0000] \"GET /budget/overview HTTP/1.1\" 404 130 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:51:3351"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:51 +0000] \"GET /budget/categories HTTP/1.1\" 404 132 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:51:3351"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:51 +0000] \"GET /financial/budget HTTP/1.1\" 404 131 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:51:3351"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:03:51 +0000] \"GET /financial/budget HTTP/1.1\" 404 131 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:33:51:3351"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:04:00 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:34:00:340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:04:00 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 169 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:34:00:340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:04:00 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 266 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:34:00:340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:04:00 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 805 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:34:00:340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:04:00 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 667 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:34:00:340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:04:00 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:34:00:340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:04:00 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 709 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:34:00:340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:04:00 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 151 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:34:00:340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:04:00 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 270 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:34:00:340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:04:00 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 340 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:34:00:340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:04:00 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 940 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:34:00:340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:05:24 +0000] \"GET /api/projects?limit=5000&page=1 HTTP/1.1\" 200 170 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:35:24:3524"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:21:4721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:22 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:22:4722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:22 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 169 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:22:4722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:22 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 667 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:22:4722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:22 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 266 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:22:4722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:22 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:22:4722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:22 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 805 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:22:4722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:22 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 340 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:22:4722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:22 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 709 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:22:4722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:22 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 151 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:22:4722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:22 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 270 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:22:4722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:17:22 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 940 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:47:22:4722"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:18:15 +0000] \"GET /api/projects?limit=5000&page=1 HTTP/1.1\" 200 170 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:48:15:4815"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:18:17 +0000] \"GET /api/projects?limit=5000&page=1 HTTP/1.1\" 200 170 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:48:17:4817"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:18:21 +0000] \"GET /api/projects?page=1&limit=10&sortBy=updatedAt&sortOrder=desc HTTP/1.1\" 200 169 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:48:21:4821"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:18:21 +0000] \"GET /api/projects?page=1&limit=10&sortBy=updatedAt&sortOrder=desc HTTP/1.1\" 200 169 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:48:21:4821"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:18:23 +0000] \"GET /api/clients/dropdown HTTP/1.1\" 200 235 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:48:23:4823"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReverse geocoding successful for coordinates: 12.9716, 77.5946\u001b[39m","timestamp":"2025-06-08 21:48:24:4824"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:18:24 +0000] \"POST /api/maps/reverse-geocode HTTP/1.1\" 200 200 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:48:24:4824"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReverse geocoding successful for coordinates: 12.9716, 77.5946\u001b[39m","timestamp":"2025-06-08 21:49:40:4940"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:40 +0000] \"POST /api/maps/reverse-geocode HTTP/1.1\" 200 200 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:40:4940"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mGeocoding successful for address: 67/1, KG Halli, D' Souza Layout, Ashok Nagar, Bengaluru, Karnataka 560002, India\u001b[39m","timestamp":"2025-06-08 21:49:45:4945"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mUpdated finances for project CYM-3: profit=100, received=0, pending=100\u001b[39m","timestamp":"2025-06-08 21:49:45:4945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mPending income created for project: CYM-3\u001b[39m","timestamp":"2025-06-08 21:49:45:4945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAuto-updated project status: CYM-3 -> IN_PROGRESS\u001b[39m","timestamp":"2025-06-08 21:49:45:4945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProject created: CYM-3 - Hi\u001b[39m","timestamp":"2025-06-08 21:49:45:4945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:45 +0000] \"POST /api/projects HTTP/1.1\" 201 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:45:4945"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:46 +0000] \"GET /api/projects?page=1&limit=10&sortBy=updatedAt&sortOrder=desc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:46:4946"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:48 +0000] \"GET /api/calendar/events/range?startDate=2025-05-31T18%3A30%3A00.000Z&endDate=2025-06-29T18%3A30%3A00.000Z HTTP/1.1\" 200 122 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:48:4948"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:48 +0000] \"GET /api/projects?limit=1000&page=1 HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:48:4948"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:52 +0000] \"GET /api/financial/expenses?limit=50 HTTP/1.1\" 200 176 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:52:4952"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:52 +0000] \"GET /api/financial/expenses?limit=50 HTTP/1.1\" 200 176 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:52:4952"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:54 +0000] \"GET /api/financial/expenses?limit=50 HTTP/1.1\" 200 176 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:55 +0000] \"GET /api/financial/income?limit=50 HTTP/1.1\" 200 478 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:55 +0000] \"GET /api/financial/income?limit=50 HTTP/1.1\" 200 478 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:55 +0000] \"GET /api/financial/income/chart-data?period=6months HTTP/1.1\" 200 393 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:55 +0000] \"GET /api/financial/income/chart-data?period=6months HTTP/1.1\" 200 393 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:56 +0000] \"GET /api/projects?page=1&limit=10&sortBy=updatedAt&sortOrder=desc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:56:4956"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:57 +0000] \"GET /api/financial/income?limit=50 HTTP/1.1\" 200 478 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:57:4957"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:19:57 +0000] \"GET /api/financial/income/chart-data?period=6months HTTP/1.1\" 200 393 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:49:57:4957"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:00 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 272 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:00 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:00 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:00 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 809 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:00 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:00 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 342 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:00 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:00 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 151 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:00 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 270 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:00 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 709 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:00 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 940 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:47 +0000] \"GET /api/projects/status/ongoing?page=1&limit=10 HTTP/1.1\" 200 190 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:47:5247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:50 +0000] \"GET /api/projects/status/pending?page=1&limit=10 HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:50:5250"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:52 +0000] \"GET /api/projects/status/completed?page=1&limit=10 HTTP/1.1\" 200 192 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:52:5252"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:22:52 +0000] \"GET /api/projects/status/ongoing?page=1&limit=10 HTTP/1.1\" 200 190 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:52:52:5252"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:23:24 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:53:24:5324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:23:24 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:53:24:5324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:12 +0000] \"GET /api/clients?search=C&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:12:5412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:12 +0000] \"GET /api/clients?search=Cr&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:12:5412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:12 +0000] \"GET /api/clients?search=Cre&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:12:5412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:15 +0000] \"GET /api/clients?search=Cr&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:15:5415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:15 +0000] \"GET /api/clients?search=C&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:15:5415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:15 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:15:5415"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:19 +0000] \"GET /api/projects?limit=5000&page=1 HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:19:5419"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:27 +0000] \"GET /api/projects?page=1&limit=10&sortBy=updatedAt&sortOrder=desc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:27:5427"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:32 +0000] \"GET /api/financial/income?limit=50 HTTP/1.1\" 200 478 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:32:5432"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:32 +0000] \"GET /api/financial/income/chart-data?period=6months HTTP/1.1\" 200 393 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:32:5432"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:36 +0000] \"GET /api/financial/income?limit=50 HTTP/1.1\" 200 478 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:36:5436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:36 +0000] \"GET /api/financial/income/chart-data?period=6months HTTP/1.1\" 200 393 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:36:5436"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:37 +0000] \"GET /api/financial/expenses?limit=50 HTTP/1.1\" 200 176 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:37:5437"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:39 +0000] \"GET /api/projects?limit=100 HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:39:5439"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExpense created: Bria - ₹30\u001b[39m","timestamp":"2025-06-08 21:54:54:5454"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:54 +0000] \"POST /api/financial/expenses HTTP/1.1\" 201 349 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:54:5454"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:55 +0000] \"GET /api/financial/expenses?limit=50 HTTP/1.1\" 200 415 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:55:5455"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:24:57 +0000] \"GET /api/projects?limit=100 HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:54:57:5457"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mUpdated finances for project CYM-3: profit=-200, received=100, pending=0\u001b[39m","timestamp":"2025-06-08 21:55:11:5511"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExpense created: Bike - ₹300\u001b[39m","timestamp":"2025-06-08 21:55:11:5511"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:11 +0000] \"POST /api/financial/expenses HTTP/1.1\" 201 387 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:11:5511"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:12 +0000] \"GET /api/financial/expenses?limit=50 HTTP/1.1\" 200 693 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:12:5512"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:16 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:16:5516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:16 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:16:5516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:16 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:16:5516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:16 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:16:5516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:16 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:16:5516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:16 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:16:5516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:16 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:16:5516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:16 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:16:5516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:16 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:16:5516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:16 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:16:5516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:25:16 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 21:55:16:5516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase logging enabled for development\u001b[39m","timestamp":"2025-06-08 22:01:52:152"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-08 22:01:58:158"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Cymatics Backend Server is running on port 3000\u001b[39m","timestamp":"2025-06-08 22:01:58:158"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-08 22:01:58:158"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:3000/health\u001b[39m","timestamp":"2025-06-08 22:01:58:158"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API docs: http://localhost:3000/api\u001b[39m","timestamp":"2025-06-08 22:01:58:158"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Network access: http://0.0.0.0:3000\u001b[39m","timestamp":"2025-06-08 22:01:58:158"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEmail service connected successfully\u001b[39m","timestamp":"2025-06-08 22:02:00:20"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:00:120"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:01 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:02 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:02:122"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:02 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:02:122"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:02 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:02:122"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:02 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:02:122"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:02 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:02:122"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:02 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:02:122"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:02 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:02:122"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:02 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:02:122"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:02 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:02:122"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:02 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:02:122"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:02 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:02:122"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:06 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:06:126"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:06 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:06:126"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:06 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:06:126"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:06 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:06:126"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:06 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:06:126"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:06 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:06:126"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:06 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:06:126"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:06 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:06:126"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:06 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:06:126"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:06 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:06:126"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:06 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:06:126"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:10 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:10:1210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:10 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:10:1210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:10 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:10:1210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:10 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:10:1210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:10 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:10:1210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:10 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:10:1210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:10 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:10:1210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:10 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:10:1210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:10 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:10:1210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:10 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:10:1210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:10 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:10:1210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:46 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:46:1246"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:46 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:46:1246"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:46 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:46:1246"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:46 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:46:1246"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:46 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:46:1246"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:46 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:46:1246"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:46 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:46:1246"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:46 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:46:1246"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:46 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:46:1246"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:46 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:46:1246"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:46 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:46:1246"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:48 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:48:1248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:48 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:48:1248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:48 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:48:1248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:48 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:48:1248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:48 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:48:1248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:48 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:48:1248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:48 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:48:1248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:48 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:48:1248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:48 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:48:1248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:48 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:48:1248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:42:48 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:12:48:1248"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:02 +0000] \"GET /api/projects?page=1&limit=10&sortBy=updatedAt&sortOrder=desc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:02:142"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:02 +0000] \"GET /api/projects?page=1&limit=10&sortBy=updatedAt&sortOrder=desc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:02:142"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:07 +0000] \"GET /api/projects/status/ongoing?page=1&limit=10 HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:07:147"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:09 +0000] \"GET /api/projects/status/pending?page=1&limit=10 HTTP/1.1\" 200 190 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:09:149"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:10 +0000] \"GET /api/projects/status/completed?page=1&limit=10 HTTP/1.1\" 200 346 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:10:1410"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:13 +0000] \"GET /api/projects/status/pending?page=1&limit=10 HTTP/1.1\" 200 190 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:13:1413"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:14 +0000] \"GET /api/projects/status/ongoing?page=1&limit=10 HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:14:1414"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:16 +0000] \"GET /api/projects?page=1&limit=10&sortBy=updatedAt&sortOrder=desc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:16:1416"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:19 +0000] \"GET /api/projects/code/CYM-3 HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:19:1419"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:24 +0000] \"GET /api/projects?page=1&limit=10&sortBy=updatedAt&sortOrder=desc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:24:1424"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:53 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:53:1453"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:53 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:53:1453"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:53 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:53:1453"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:44:55 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:14:55:1455"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:45:25 +0000] \"GET /api/clients?search=%2C&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:15:25:1525"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:45:26 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:15:26:1526"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:45:28 +0000] \"GET /api/clients?search=C&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:15:28:1528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:45:29 +0000] \"GET /api/clients?search=Cre&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:15:29:1529"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:46:16 +0000] \"GET /api/calendar/events/range?startDate=2025-05-31T18%3A30%3A00.000Z&endDate=2025-06-29T18%3A30%3A00.000Z HTTP/1.1\" 200 122 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:16:16:1616"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:46:16 +0000] \"GET /api/projects?limit=1000&page=1 HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:16:16:1616"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:46:18 +0000] \"GET /api/calendar/events/range?startDate=2025-05-31T18%3A30%3A00.000Z&endDate=2025-06-29T18%3A30%3A00.000Z HTTP/1.1\" 200 122 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:16:18:1618"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:46:18 +0000] \"GET /api/projects?limit=1000&page=1 HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:16:18:1618"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:16:48:53 +0000] \"GET /api/projects?limit=5000&page=1 HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:18:53:1853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase logging enabled for development\u001b[39m","timestamp":"2025-06-08 22:27:39:2739"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-08 22:27:45:2745"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Cymatics Backend Server is running on port 3000\u001b[39m","timestamp":"2025-06-08 22:27:45:2745"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-08 22:27:45:2745"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 Health check: http://localhost:3000/health\u001b[39m","timestamp":"2025-06-08 22:27:45:2745"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API docs: http://localhost:3000/api\u001b[39m","timestamp":"2025-06-08 22:27:45:2745"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Network access: http://0.0.0.0:3000\u001b[39m","timestamp":"2025-06-08 22:27:45:2745"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEmail service connected successfully\u001b[39m","timestamp":"2025-06-08 22:27:49:2749"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:16 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:16:3316"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:16 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:16:3316"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:16 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:16:3316"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:16 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:16:3316"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:16 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:16:3316"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:16 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:16:3316"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:16 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:16:3316"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:16 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:16:3316"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:16 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:16:3316"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:16 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:16:3316"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:16 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:16:3316"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:26:3326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:26:3326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:26:3326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:26:3326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:26:3326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:26:3326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:26:3326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:26:3326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:26:3326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:26:3326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:26:3326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:26:3326"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:27 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:28 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:28:3328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:28 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:28:3328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:28 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:28:3328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:28 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:28:3328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:28 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:28:3328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:28 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:28:3328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:28 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:28:3328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:28 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:28:3328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:28 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:28:3328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:28 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:28:3328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:28 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:28:3328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:31 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:31:3331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:31 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:31:3331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:31 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:31:3331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:31 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:31:3331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:31 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:31:3331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:31 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:31:3331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:31 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:31:3331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:31 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:31:3331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:31 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:31:3331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:31 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:31:3331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:03:31 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:33:31:3331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:04:19 +0000] \"GET /api/projects/status/ongoing?page=1&limit=10 HTTP/1.1\" 200 190 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:34:19:3419"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:04:22 +0000] \"GET /api/projects/status/pending?page=1&limit=10 HTTP/1.1\" 200 190 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:34:22:3422"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:04:22 +0000] \"GET /api/projects/status/completed?page=1&limit=10 HTTP/1.1\" 200 192 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:34:22:3422"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:05:02 +0000] \"GET /api/calendar/events/range?startDate=2025-05-31T18%3A30%3A00.000Z&endDate=2025-06-29T18%3A30%3A00.000Z HTTP/1.1\" 200 122 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:35:02:352"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:05:02 +0000] \"GET /api/projects?limit=1000&page=1 HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:35:02:352"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:05:03 +0000] \"GET /api/calendar/events/range?startDate=2025-05-31T18%3A30%3A00.000Z&endDate=2025-06-29T18%3A30%3A00.000Z HTTP/1.1\" 200 122 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:35:03:353"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:05:03 +0000] \"GET /api/projects?limit=1000&page=1 HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:35:03:353"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:06:07 +0000] \"GET /api/projects?limit=5000&page=1 HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:36:07:367"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:06:13 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:36:13:3613"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:06:13 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:36:13:3613"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:06:13 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:36:13:3613"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:06:14 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:36:14:3614"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:06:18 +0000] \"GET /api/clients?search=Hi&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:36:18:3618"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:08:11 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:38:11:3811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:08:11 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:38:11:3811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:08:11 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:38:11:3811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:08:11 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:38:11:3811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:08:11 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:38:11:3811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:08:11 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:38:11:3811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:08:11 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:38:11:3811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:08:11 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:38:11:3811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:08:11 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:38:11:3811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:08:11 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:38:11:3811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:08:11 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:38:11:3811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:03:473"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:04 +0000] \"GET /api/auth/profile HTTP/1.1\" 200 260 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:04:474"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:05 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:05:475"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:05 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:05:475"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:05 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:05:475"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:05 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:05:475"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:05 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:05:475"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:05 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:05:475"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:05 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:05:475"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:05 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:05:475"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:05 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:05:475"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:05 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:05:475"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:05 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:05:475"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:09 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:09 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:09 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:09 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:09 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:09 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:09 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:09 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:09 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:09 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:09 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:09:479"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:12 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:12:4712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:12 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:12:4712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:12 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:12:4712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:12 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:12:4712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:12 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:12:4712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:12 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:12:4712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:12 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:12:4712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:12 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:12:4712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:12 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:12:4712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:12 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:12:4712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:12 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:12:4712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:31 +0000] \"GET /api/projects/status/ongoing?page=1&limit=10 HTTP/1.1\" 200 190 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:31:4731"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:35 +0000] \"GET /api/projects/status/pending?page=1&limit=10 HTTP/1.1\" 200 190 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:35:4735"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:36 +0000] \"GET /api/projects/status/completed?page=1&limit=10 HTTP/1.1\" 200 192 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:36:4736"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:39 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:39:4739"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:39 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:39:4739"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:39 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:39:4739"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:41 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:41:4741"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:45 +0000] \"GET /api/clients?search=C&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:45:4745"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:45 +0000] \"GET /api/clients?search=Cr&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:45:4745"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:46 +0000] \"GET /api/clients?search=Cre&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:46:4746"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:46 +0000] \"GET /api/clients?search=Crea&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:46:4746"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:48 +0000] \"GET /api/clients?search=Creat&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:48:4748"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:49 +0000] \"GET /api/clients?search=Creative&limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:49:4749"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:51 +0000] \"GET /api/clients?limit=50 HTTP/1.1\" 200 754 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:51:4751"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:56 +0000] \"GET /api/calendar/events/range?startDate=2025-05-31T18%3A30%3A00.000Z&endDate=2025-06-29T18%3A30%3A00.000Z HTTP/1.1\" 200 122 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:56:4756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:17:56 +0000] \"GET /api/projects?limit=1000&page=1 HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:47:56:4756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:46 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:46:4846"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:46 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:46:4846"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:46 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:46:4846"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:46 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:46:4846"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:46 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:46:4846"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:46 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:46:4846"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:46 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:46:4846"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:46 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:46:4846"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:46 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:46:4846"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:46 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:46:4846"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:46 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:46:4846"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:55 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:55:4855"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:55 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:55:4855"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:55 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:55:4855"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:55 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:55:4855"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:55 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:55:4855"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:55 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:55:4855"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:55 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:55:4855"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:55 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:55:4855"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:55 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:55:4855"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:55 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:55:4855"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:55 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:55:4855"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:57 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:57:4857"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:57 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:57:4857"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:57 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:57:4857"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:57 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:57:4857"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:57 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:57:4857"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:57 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:57:4857"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:57 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:57:4857"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:57 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:57:4857"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:57 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:57:4857"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:57 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:57:4857"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:57 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:57:4857"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:59 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:59:4859"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:59 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:59:4859"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:59 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:59:4859"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:59 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:59:4859"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:59 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:59:4859"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:59 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:59:4859"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:59 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:59:4859"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:59 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:59:4859"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:59 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:59:4859"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:59 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:59:4859"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:18:59 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:48:59:4859"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:19:01 +0000] \"GET /api/dashboard/today-schedule HTTP/1.1\" 200 117 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:49:01:491"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:19:01 +0000] \"GET /api/dashboard/stats HTTP/1.1\" 200 273 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:49:01:491"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:19:01 +0000] \"GET /api/projects?limit=10&sortBy=shootStartDate&sortOrder=asc HTTP/1.1\" 200 - \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:49:01:491"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:19:01 +0000] \"GET /api/dashboard/charts/monthly-income-expense HTTP/1.1\" 200 344 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:49:01:491"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:19:01 +0000] \"GET /api/dashboard/charts/monthly-projects HTTP/1.1\" 200 289 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:49:01:491"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:19:01 +0000] \"GET /api/dashboard/charts/income-expense?period=6months HTTP/1.1\" 200 813 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:49:01:491"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:19:01 +0000] \"GET /api/dashboard/charts/project-details HTTP/1.1\" 200 686 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:49:01:491"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:19:01 +0000] \"GET /api/dashboard/charts/expense-breakdown?period=6months HTTP/1.1\" 200 742 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:49:01:491"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:19:01 +0000] \"GET /api/dashboard/charts/expense-pie HTTP/1.1\" 200 180 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:49:01:491"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:19:01 +0000] \"GET /api/dashboard/charts/monthly-expenses-stacked HTTP/1.1\" 200 453 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:49:01:491"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m*************** - - [08/Jun/2025:17:19:01 +0000] \"GET /api/dashboard/charts/category-expenses HTTP/1.1\" 200 1002 \"-\" \"okhttp/4.12.0\"\u001b[39m","timestamp":"2025-06-08 22:49:01:491"}
